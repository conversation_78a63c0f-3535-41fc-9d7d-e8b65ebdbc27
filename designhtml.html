<!DOCTYPE html>
    <html>
    <head>
    <meta charset="UTF-8">
      <script src="https://cdn.tailwindcss.com"></script>
      <link href="https://cdn.jsdelivr.net/npm/daisyui@4/dist/full.min.css" rel="stylesheet" type="text/css" />
      <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
      <script>
  tailwind.config = {
    darkMode: 'class'
  }
  </script>
    </head>
    <body>
<div class="min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-colors duration-500">
  <!-- Light/Dark Mode Toggle -->
  <div class="absolute top-4 right-4">
    <button id="theme-toggle" class="p-3 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
      <i data-lucide="sun" class="w-5 h-5 text-amber-500 dark:hidden"></i>
      <i data-lucide="moon" class="w-5 h-5 text-indigo-400 hidden dark:block"></i>
    </button>
  </div>

  <!-- Main Content -->
  <div class="flex flex-col items-center justify-center min-h-screen p-6">
    <!-- Puppy Image -->
    <div class="relative mb-8">
      <div class="w-32 h-32 rounded-full bg-gradient-to-br from-rose-300 to-purple-300 dark:from-rose-600 dark:to-purple-600 p-1 animate-pulse">
        <img src="https://picsum.photos/id/237/200/200" alt="Puppy" class="w-full h-full rounded-full object-cover border-4 border-white dark:border-gray-800" />
      </div>
      <div class="absolute -top-2 -right-2 w-8 h-8 bg-rose-400 rounded-full flex items-center justify-center animate-bounce">
        <i data-lucide="heart" class="w-4 h-4 text-white"></i>
      </div>
    </div>

    <!-- Title -->
    <h1 class="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-2 text-center">
      Puppy Arrival
    </h1>
    <p class="text-lg text-gray-600 dark:text-gray-300 mb-8 text-center">
      Our furry friend is on the way! 🐾
    </p>

    <!-- Countdown Timer -->
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl shadow-xl p-8 md:p-12">
      <div id="countdown" class="grid grid-cols-4 gap-4 md:gap-8">
        <div class="text-center">
          <div id="days" class="text-4xl md:text-6xl font-bold text-rose-500 dark:text-rose-400">00</div>
          <div class="text-sm text-gray-600 dark:text-gray-300 mt-2 uppercase tracking-wider">Days</div>
        </div>
        <div class="text-center">
          <div id="hours" class="text-4xl md:text-6xl font-bold text-purple-500 dark:text-purple-400">00</div>
          <div class="text-sm text-gray-600 dark:text-gray-300 mt-2 uppercase tracking-wider">Hours</div>
        </div>
        <div class="text-center">
          <div id="minutes" class="text-4xl md:text-6xl font-bold text-indigo-500 dark:text-indigo-400">00</div>
          <div class="text-sm text-gray-600 dark:text-gray-300 mt-2 uppercase tracking-wider">Minutes</div>
        </div>
        <div class="text-center">
          <div id="seconds" class="text-4xl md:text-6xl font-bold text-pink-500 dark:text-pink-400">00</div>
          <div class="text-sm text-gray-600 dark:text-gray-300 mt-2 uppercase tracking-wider">Seconds</div>
        </div>
      </div>
    </div>

    <!-- Paw Prints Decoration -->
    <div class="absolute bottom-8 left-8 opacity-20">
      <i data-lucide="paw-print" class="w-12 h-12 text-rose-400"></i>
    </div>
    <div class="absolute top-8 right-8 opacity-20">
      <i data-lucide="paw-print" class="w-8 h-8 text-purple-400"></i>
    </div>
    <div class="absolute bottom-16 right-16 opacity-20">
      <i data-lucide="bone" class="w-6 h-6 text-amber-400"></i>
    </div>
  </div>
</div>

<script>
const targetDate = new Date();
    targetDate.setDate(targetDate.getDate() + 20);
  // Countdown Timer
  function updateCountdown() {
    
    
    const now = new Date().getTime();
    const distance = targetDate.getTime() - now;
    
    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((distance % (1000 * 60)) / 1000);
    
    document.getElementById('days').textContent = days.toString().padStart(2, '0');
    document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
    document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
    document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
  }
  
  // Update countdown every second
  updateCountdown();
  setInterval(updateCountdown, 1000);
  
  // Theme toggle
  const themeToggle = document.getElementById('theme-toggle');
  const html = document.documentElement;
  
  themeToggle.addEventListener('click', () => {
    html.classList.toggle('dark');
  });
  
  // Initialize icons
  lucide.createIcons();
</script>
</body>
    </html>