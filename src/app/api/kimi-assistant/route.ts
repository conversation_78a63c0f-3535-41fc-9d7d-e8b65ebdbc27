import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenAI, Type } from "@google/genai";
import { designSpecExample, htmlDesignExample } from '@/app/data/output_examples';
import OpenAI from "openai";

interface ResponseData {
  research: string;
  HTMLDesigns?: string;
}

interface TavilyResponse {
  results: Array<{
    url: string;
    title: string;
    content: string;
  }>;
}

const researcherModel = "openai/gpt-oss-20b"
const designerModel = "moonshotai/kimi-k2-instruct"

// Initialize OpenAI client for Groq with Kimi-K2
// Initialize OpenAI client for Groq with Kimi-K2 https://openrouter.ai/api/v1 https://api.groq.com/openai/v1
// groq ********************************************************
// openrouter 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
const client = new OpenAI({
  apiKey: process.env.GROQ_API_KEY || "********************************************************",
  baseURL: "https://api.groq.com/openai/v1"
});

// Tavily search function
async function searchTavily(query: string): Promise<TavilyResponse> {
  console.log('🔍 [Tavily] Initiating search with query:', query);
  
  try {
    const response = await fetch('https://api.tavily.com/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.TAVILY_TOKEN}`
      },
      body: JSON.stringify({
        query,
        search_depth: "advanced",
        max_results: 5
      })
    });

    if (!response.ok) {
      console.error('❌ [Tavily] API error:', {
        status: response.status,
        statusText: response.statusText
      });
      throw new Error(`Tavily API error: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ [Tavily] Search completed successfully with', data.results.length, 'results');
    
    // Only return the fields we need
    return {
      results: data.results.map((result: any) => ({
        url: result.url,
        title: result.title,
        content: result.content
      }))
    };
  } catch (error) {
    console.error('❌ [Tavily] Search failed:', error);
    throw error;
  }
}

// Tavily function declaration for OpenAI
const tavilySearchFunctionDeclaration = {
  type: "function" as const,
  function: {
    name: 'search_tavily',
    description: 'Searches the web for real-time information using Tavily API',
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: 'The search query to find relevant information',
        }
      },
      required: ['query'],
    },
  }
};


export async function POST(request: NextRequest) {
  console.log('📝 [UX Assistant] Received new request');
  
  try {
    const { chatInput } = await request.json()
    console.log('🤖 [UX Assistant] Processing request:', chatInput);

    const message = chatInput;

    // Initialize researcher with function calling capability
    console.log('🔬 [Researcher] Starting research phase');
    const researcher_response = await client.chat.completions.create({
      model: researcherModel,
      messages: [
        {
          role: "system",
          content: "You are a helpful ux research assistant. You use the tool to get best practices for ux design implementations. Frame the question in a way that asks about the use of the patterns or approaches within ui designs. We are looking for best practices. The User's Design Request will be going to a designer, your job is to provide the research needed so that the designer can make the best design possible. You are providing research finding, not design direction. Your research is crucial to the success of the design, so use your intelligence to ask the right question. Make your question extremely concise and to the point as if you were entering it into a search engine. You ALWAYS use the tool. Don't use any quotes or special characters in your tool query. Your final response should be helpful research findings written using markdown."
        },
        {
          role: "user",
          content: "User's Design Request, for the designer: " + message
        }
      ],
      tools: [tavilySearchFunctionDeclaration],
      tool_choice: "auto"
    });

    let research = "";

    // Handle function calls if present (OpenAI format)
    const toolCalls = researcher_response.choices[0]?.message?.tool_calls;
    if (toolCalls && toolCalls.length > 0) {
      const toolCall = toolCalls[0];
      console.log('🔄 [Researcher] Function call detected:', toolCall.function.name);

      if (toolCall.function.name === 'search_tavily') {
        try {
          const args = JSON.parse(toolCall.function.arguments);
          if (args.query && typeof args.query === 'string') {
            console.log('🔍 [Researcher] Executing Tavily search with query:', args.query);
            // Execute the search
            const searchResult = await searchTavily(args.query);

            //console.log('✅ [Tavily] Search completed successfully with', searchResult);

            console.log('📚 [Researcher] Generating final research summary');
            // Get final response incorporating search results
            const finalResponse = await client.chat.completions.create({
              model: researcherModel,
              messages: [
                {
                  role: "system",
                  content: "You are a UX research assistant. Based on the search results provided, synthesize them into a comprehensive research summary written in markdown format."
                },
                {
                  role: "user",
                  content: `

Search Results:
${JSON.stringify(searchResult, null, 2)}

Please synthesize these search results into a comprehensive research summary.
- list sources.
- Do not provide specific design direction. 

Provide your response in markdown format.`
                }
              ]
            });

            research = finalResponse.choices[0]?.message?.content || "";
            console.log('✅ [Researcher] Research phase completed successfully:', research);
          }
        } catch (error) {
          console.error('❌ [Researcher] Error during research phase:', error);
          research = researcher_response.choices[0]?.message?.content || "";
        }
      }
    } else {
      console.log('ℹ️ [Researcher] No function calls detected, using direct response');
      research = researcher_response.choices[0]?.message?.content || "";
    }


    console.log('💻 [Designer] Starting HTML/CSS implementation phase');

const systemMessage2 = `You are a Senior Elite UX/UI Frontend designer with 10+ years of experience creating stunning, modern interfaces. Your output will be injected into an iframe with DaisyUI, TailwindCSS, and Lucide icons pre-loaded.

You first consider the design request and then use anything useful or relevant from the research to inform your design decisions. You do not use any research that is not directly relevant to the design request.

## CRITICAL RULES:
- ONLY provide HTML for <body> content (no DOCTYPE, html, head, or body tags)
- Output goes into: <body>\${htmlCode}</body>

## DESIGN STYLE: A **perfect balance** between **elegant minimalism** and **functional design**.

## DESIGN EXCELLENCE PRINCIPLES:

### 1. VISUAL HIERARCHY & TYPOGRAPHY
- Use clear size relationships: text-6xl → text-4xl → text-2xl → text-lg → text-base
- Leverage font weights strategically: font-bold for headings, font-semibold for emphasis
- Create breathing room with proper line-height: leading-tight, leading-relaxed

### 2. SOPHISTICATED COLOR & CONTRAST
- Use DaisyUI semantic colors: primary, secondary, accent, neutral
- Layer transparencies for depth: bg-base-100/80, text-base-content/60
- Create gradients: bg-gradient-to-br from-primary to-secondary
- Ensure proper contrast ratios for accessibility

### 3. PROFESSIONAL SPACING & LAYOUT
- Use consistent spacing scale: p-4, p-6, p-8, p-12 (never odd numbers)
- Create visual groups with gap-4, gap-6, gap-8
- Use max-width containers: max-w-4xl, max-w-6xl mx-auto
- Implement proper grid systems: grid-cols-1 md:grid-cols-2 lg:grid-cols-3

### 4. MODERN DESIGN PATTERNS
- Glass morphism: bg-white/10 backdrop-blur-lg
- Subtle shadows: shadow-lg, shadow-xl, shadow-2xl
- Rounded corners: rounded-lg, rounded-xl, rounded-2xl
- Smooth transitions: transition-all duration-300
- Hover states: hover:scale-105, hover:shadow-2xl

### 5. PREMIUM INTERACTIONS
\`\`\`html
<!-- Animated buttons -->
<button class="btn btn-primary hover:scale-105 transition-transform duration-300">
  <i data-lucide="sparkles" class="w-4 h-4"></i>
  Get Started
</button>

<!-- Card hover effects -->
<div class="card bg-base-100 shadow-lg hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">

<!-- Hero with animated elements -->
<div class="hero min-h-screen bg-gradient-to-br from-primary/20 via-secondary/10 to-accent/20">
  <div class="hero-content text-center max-w-4xl">
    <div class="animate-fade-in">
\`\`\`

### 6. PROFESSIONAL LAYOUTS
- **SaaS Landing**:
    1.  **Classic Conversion**: Hero (large image/video background) + 3-column feature grid with icons + Testimonials carousel + Simple CTA block.
    2.  **Modern Scroll-Driven**: Scroll-triggered animations, alternating image/text sections, subtle gradients, integrated social proof.
    3.  **Product Showcase**: Hero with interactive product demo/screenshot + detailed feature sections with benefit-oriented text + use cases + customer logos.
    4.  **Minimalist Impact**: Bold typography hero, single prominent CTA, scroll reveals for key value propositions, subtle background patterns.
- **Dashboard**:
    1.  **Analytics Focus**: Left sidebar (collapsed/expanded) + Main content with data visualization (charts, graphs) + Key performance indicator (KPI) cards + Recent activity feed.
    2.  **Task/Project Management**: Top navigation + Sidebar with projects/teams + Main area with Kanban boards or detailed task lists + Progress indicators.
    3.  **Admin Panel**: Collapsible sidebar + Table-heavy main content with pagination/filters + User management cards + System alerts/notifications.
    4.  **Activity Stream**: Centralized feed of user updates/notifications, contextual sidebar with related information, quick action buttons.
- **E-commerce**:
    1.  **Grid-Centric Store**: Prominent product grid (3-4 columns) with quick-view modals + Left sidebar filters (price, category, brand) + Sort options.
    2.  **Category Highlight**: Large hero for category promotion + Featured products carousel + Horizontal filter bar + Infinite scroll product cards.
    3.  **Product Detail Page**: Large image gallery + Product description tabs (details, specs, reviews) + Add to cart with quantity selector + Related products section.
    4.  **Editorial Shopping**: Blended product sections with lifestyle imagery, curated collections, storytelling about product origins or themes.
- **Portfolio**:
    1.  **Masonry Grid Showcase**: Responsive masonry grid for project cards + Filters by skill/type + Dedicated project detail pages.
    2.  **Full-screen Immersive**: Full-width hero with project preview carousel + Brief intro + Prominent links to "About" and "Contact."
    3.  **Categorized Sections**: Projects grouped by type (e.g., "Web Design," "Branding," "UX/UI") with distinct section headings and layouts.
    4.  **Interactive Showcase**: Interactive elements on hover (e.g., video previews, image cycles), scroll-triggered project reveals, immersive case studies.
- **Blog/Content Platform**:
    1.  **Classic Blog Feed**: Hero with featured article + Grid of recent posts + Categories sidebar + Pagination.
    2.  **Magazine Style Layout**: Asymmetrical grid layout for articles + Large hero section for lead stories + Integrated newsletter signup.
    3.  **Minimalist Reader View**: Clean single article view with wide text column + Prominent title/author + Social share buttons + Related posts.
    4.  **Content Hub**: Topic-based navigation, prominent search bar, curated article lists, author profiles with their published articles.
- **Settings/Account Management**:
    1.  **Tabbed Interface**: Vertical or horizontal tabs for different sections (Profile, Security, Notifications, Billing) + Forms within each tab.
    2.  **List-Based Navigation**: A clear list of settings categories (e.g., "General," "Privacy," "Integrations") leading to dedicated sub-pages or sections.
    3.  **Card Layout**: Each setting group (e.g., "Account Details," "Password," "Connected Apps") represented as a distinct card with toggles, inputs, or buttons.
    4.  **Wizard-style Setup**: Multi-step flow for initial setup or complex configuration changes, with clear progress indicators and validation.
- **Pricing Page**:
    1.  **Tiered Columns**: 3-4 price columns with a highlighted "recommended" plan + Detailed feature comparison table below + FAQ accordion.
    2.  **Toggleable Periods**: Prominent toggle for yearly/monthly pricing + Smaller feature breakdown below each plan + Customer logos/testimonials.
    3.  **Feature-Driven Pricing**: Focus on what specific features each tier unlocks with clear visual indicators + Option for custom plans/contact sales.
    4.  **Value Proposition Led**: Hero section emphasizes ROI/benefits first, then introduces plans as solutions, rather than just raw prices.

### 7. MOBILE-FIRST RESPONSIVE
- Start with mobile (450px): single column, larger touch targets
- sm: (640px+): 2-column layouts, side navigation
- md: (768px+): 3-column grids, horizontal navigation  
- lg: (1024px+): Complex layouts, sidebars

### 8. STUNNING EXAMPLES
\`\`\`html
<!-- Premium Header with User Profile Dropdown -->
<header class="navbar bg-base-100 shadow-xl rounded-box p-4 max-w-7xl mx-auto backdrop-blur-sm bg-opacity-80 border border-base-200/50">
  <div class="navbar-start">
    <a class="btn btn-ghost text-2xl font-extrabold text-primary hover:bg-transparent">
      <i data-lucide="sparkles" class="w-7 h-7 mr-2 text-secondary"></i>
      <span class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">AppLogo</span>
    </a>
  </div>
  <div class="navbar-center hidden lg:flex">
    <ul class="menu menu-horizontal p-0 font-semibold text-base-content/80">
      <li><a class="hover:text-primary transition-colors duration-200 px-4 py-2 rounded-lg">Dashboard</a></li>
      <li><a class="hover:text-primary transition-colors duration-200 px-4 py-2 rounded-lg">Projects</a></li>
      <li><a class="hover:text-primary transition-colors duration-200 px-4 py-2 rounded-lg">Reports</a></li>
      <li><a class="hover:text-primary transition-colors duration-200 px-4 py-2 rounded-lg">Settings</a></li>
    </ul>
  </div>
  <div class="navbar-end">
    <button class="btn btn-ghost btn-circle hidden md:flex mr-2">
      <i data-lucide="bell" class="w-6 h-6 text-base-content/70 hover:text-primary transition-colors duration-200"></i>
    </button>
    <div class="dropdown dropdown-end">
      <label tabindex="0" class="btn btn-ghost btn-circle avatar transition-transform duration-200 hover:scale-110">
        <div class="w-12 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2 shadow-lg">
          <img src="https://picsum.photos/id/64/100/100" alt="User Avatar" />
        </div>
      </label>
      <ul tabindex="0" class="mt-3 p-4 shadow-xl menu menu-compact dropdown-content bg-base-100 rounded-box w-52 border border-base-200/50 animate-fade-in">
        <li>
          <a class="justify-between px-4 py-2 text-base-content/80 hover:text-primary transition-colors duration-200">
            Profile
            <span class="badge badge-primary badge-sm">New</span>
          </a>
        </li>
        <li><a class="px-4 py-2 text-base-content/80 hover:text-primary transition-colors duration-200">Settings</a></li>
        <li><a class="px-4 py-2 text-base-content/80 hover:text-primary transition-colors duration-200">Logout</a></li>
      </ul>
    </div>
  </div>
</header>

<!-- Interactive Testimonial Card -->
<div class="card bg-gradient-to-br from-base-100 to-base-200 shadow-2xl rounded-3xl p-8 border border-base-300/50 transform transition-all duration-500 hover:scale-[1.02] hover:shadow-primary/30">
  <div class="card-body p-0">
    <div class="flex items-center mb-6">
      <div class="avatar">
        <div class="w-16 h-16 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2 shadow-lg">
          <img src="https://picsum.photos/id/1005/100/100" alt="User Avatar" />
        </div>
      </div>
      <div class="ml-4">
        <h4 class="text-xl font-bold text-base-content">Jane Doe</h4>
        <p class="text-base-content/60 text-sm">CEO, InnovateTech</p>
      </div>
    </div>
    <p class="text-base-content text-lg leading-relaxed italic mb-6 relative">
      <i data-lucide="quote" class="absolute -top-4 -left-4 w-12 h-12 text-primary/20 opacity-70 rotate-180 z-0"></i>
      <span class="relative z-10">"The design quality is simply unparalleled. Our users consistently praise the intuitive interface and modern aesthetic. This product has transformed our workflow."</span>
      <i data-lucide="quote" class="absolute -bottom-4 -right-4 w-12 h-12 text-primary/20 opacity-70 z-0"></i>
    </p>
    <div class="rating rating-md">
      <input type="radio" name="rating-9" class="rating-hidden" />
      <input type="radio" name="rating-9" class="mask mask-star-2 bg-warning" checked />
      <input type="radio" name="rating-9" class="mask mask-star-2 bg-warning" />
      <input type="radio" name="rating-9" class="mask mask-star-2 bg-warning" />
      <input type="radio" name="rating-9" class="mask mask-star-2 bg-warning" />
      <input type="radio" name="rating-9" class="mask mask-star-2 bg-warning" />
    </div>
  </div>
</div>
\`\`\`

## MULTI-PAGE HANDLING:
When a design needs multiple pages/sections, create a single-page application using:
- Multiple divs with unique IDs for each "page"
- JavaScript to show/hide pages: showPage('page1')
- Navigation that calls JavaScript functions
- Initially show only the first page



## TECHNICAL REQUIREMENTS:
- Images: <img src="https://picsum.photos/id/[1-100]/400/300" class="..." />
- Icons: <i data-lucide="icon-name" class="w-4 h-4"></i>
- Multi-page: Use showPage() function with hidden/visible divs
- Always end with: <script>lucide.createIcons();</script>

Create interfaces that feel premium, modern, and delightful to use.

The final output should look like production-ready, beautiful screens when rendered, reflecting the high standards expected by a Senior Elite UX/UI Frontend designer with 10+ years of experience. You always go above and beyond the requirements. We want user coming away with the feeling like they experienced something that exceeded expectations, not something that was quickly thrown together. Everyone is counting on you to produce superior and amazing work. You have decades of experience, you can do it!
`;

const usermessageAndResearch = `The design request is: "Please design this: ${message} please also include a working light/dark mode switcher, site starts in light mode." and the research is ${research}`;
const conversationMessages = [
            { role: 'system' as const, content: systemMessage2 },{ role: 'user' as const, content: usermessageAndResearch },
        ];

    const groq_response = await client.chat.completions.create({
                model: designerModel,
                messages: conversationMessages,
                temperature: 0.1,
            });

    console.log('✅ [Designer] HTML/CSS implementation completed');
            console.log ('groq_response', groq_response.choices[0]?.message.content)

    const cleaned_designer_response = (groq_response.choices[0]?.message.content as string).replace(/```html/g, '').replace(/```/g, '').replace(/<img/gi, '<img draggable="false"');



    const responseData: ResponseData = {
      research: research,
      HTMLDesigns: cleaned_designer_response,
    }

    console.log('✅ [UX Assistant] Request completed successfully');
    return NextResponse.json(responseData)
  } catch (error) {
    console.error('❌ [UX Assistant] Error processing request:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}