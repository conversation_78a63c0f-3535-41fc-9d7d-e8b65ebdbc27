'use client'
import { useState, useRef, useEffect } from 'react'
import { useEditor, createShapeId, TLShapeId } from 'tldraw'
import { toast } from 'sonner'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { ArrowUp, CircleHelp, Info, Orbit, Sparkles, X } from 'lucide-react'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { PopoverArrow, PopoverClose } from '@radix-ui/react-popover'
import { Separator } from '@radix-ui/react-separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { HighlightText } from './animate-ui/text/highlight'
import { History } from './History'
import { usePromptHistory } from '@/hooks/usePromptHistory'

interface AIResponse {
  research: string;
  HTMLDesigns?: string;
}

export function AiChatView() {
  const editor = useEditor()
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const { addPromptToHistory } = usePromptHistory()
  const loadingShapeIds = useRef<{ design: TLShapeId | null }>({
    design: null
  })
  const popoverCloseRef = useRef<HTMLButtonElement>(null)

  const tryExample = (exampleText: string) => {
    setInput(exampleText)
    popoverCloseRef.current?.click()
    sendMessage(exampleText)
  }

  const sendMessage = async (messageText?: string) => {
    const messageToSend = messageText || input.trim()
    if (!messageToSend || !editor) return

    // Add to history before sending
    addPromptToHistory(messageToSend)

    setInput('') // Clear input immediately
    setIsLoading(true)
    toast.info('Generating UI...')

    // Get viewport center coordinates
    const currentViewport = editor.getViewportPageBounds()
    const centerX = currentViewport.width / 2 + currentViewport.x
    const centerY = currentViewport.height / 2 + currentViewport.y

    // Create shape in loading state
    const designShapeId = createShapeId()

    loadingShapeIds.current = {
      design: designShapeId
    }



    // Create design shape
    editor.createShape({
      type: 'hybrid-design-mockups',
      id: designShapeId,
      x: centerX - 200,
      y: centerY,
      props: {
        html: '',
        w: 1280,
        h: 832,
        loading: true
      },
    })

    try {
      const response = await fetch('/api/kimi-assistant-prd', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatInput: messageToSend,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
      }

      const data: AIResponse = await response.json()


      // Update HTML Design Mockups shape if present
      if (data.HTMLDesigns && loadingShapeIds.current.design) {
        editor.updateShape({
          id: loadingShapeIds.current.design,
          type: 'hybrid-design-mockups',
          props: {
            html: data.HTMLDesigns,
            researchData: data.research,
            loading: false
          },
        })
      }

      toast.success('UI Generated!')

    } catch (error: any) {
      console.error('Error sending message to AI API:', error)
      toast.error(`Failed to generate UI: ${error.message}`)

    
      if (loadingShapeIds.current.design) {
        editor.deleteShape(loadingShapeIds.current.design)
      }
    } finally {
      setIsLoading(false)
      loadingShapeIds.current = { design: null }
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const handleSelectPrompt = (prompt: string) => {
    setInput(prompt)
  }

  return (
    <div
      className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-50 flex flex-col gap-2 p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg"
      style={{ width: 'clamp(300px, 50vw, 600px)' }}
    >
      <div className="flex w-full">
        <Textarea
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Start a new UI generation..."
          className="flex-grow border-none shadow-none resize-none min-h-[60px] max-h-[120px]"
          disabled={isLoading}
        />
      </div>
      <div className="flex flex-row justify-between">
        <div className="flex flex-row gap-2">
          <Popover defaultOpen={false}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="h-10 border-none hover:bg-gray-800 hover:text-white"><Info className="w-4 h-4" /><span className="hidden sm:inline ml-2">Instructions</span></Button>
            </PopoverTrigger>
            <PopoverContent
              className="flex flex-col w-[95vw] max-w-3xl h-[70vh] md:h-[80vh] lg:h-[60vh] p-0 overflow-hidden bg-gray-800 dark:bg-white border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg "
              style={{ width: 'clamp(300px, 90vw, 600px)' }}
              sideOffset={8}
              align="center"
              side="top"
            >
              <PopoverArrow className="h-4 w-8 text-white" />
              <div className="px-4 pt-4 relative h-[40px]">
                <PopoverClose ref={popoverCloseRef} asChild>
                  <Button variant="outline" className="absolute right-2 top-2 w-8 h-8  bg-transparent hover:bg-black">
                    <X className="h-4 w-4 text-white" />
                  </Button>
                </PopoverClose>
              </div>

              <div className="flex-1 overflow-hidden">
                <ScrollArea className="h-full w-full px-4 pb-8 text-white">
                  <div className="space-y-4 max-w-2xl mx-auto">
                    <div className="flex items-center gap-2 pt-2">
                      <Sparkles className="w-6 h-6 text-[#ffffff]" />
                      {/* <span className="text-xl md:text-2xl">Your AI-Powered UX Design Assistant</span> */}
                      <HighlightText className="text-xl md:text-2xl font-semibold" text="Your AI-Powered UX Design Assistant" />
                    </div>
                    <p className="mb-4 leading-relaxed">
                      Welcome! This tool is your AI co-pilot designed to accelerate your UX design process.
                      Simply describe the UI screen or flow you need. Your assistant will:
                    </p>
                    <ul className="list-disc list-inside mb-4 space-y-1 text-sm">
                      <li>Consider your request and relevant UX best practices (we do the research for you!).</li>
                      <li>Provide best practice research and generate Visual Mockups of the UI.</li>
                    </ul>
                    <p className="leading-relaxed">
                      Use this assistant to quickly ideate and visualize concepts all informed by UX insights.
                    </p>

                    <Separator className="my-6 bg-gray-400 h-[1px]" />

                    <h3 className="text-md font-semibold text-white mb-3">Try These Example Prompts:</h3>

                    <h4 className="text-sm font-semibold text-white mb-3">Simple Prompts(15-30secs to generate):</h4>

                    <ul className="list-disc list-inside space-y-2 text-sm">
                      <li className="flex items-center justify-between gap-2">
                        <span className="italic">"Design a product detail page for an e-commerce app showing images, description, price, and an "Add to Cart" button"</span>
                        <Button
                          className="bg-gray-800 dark:bg-white text-white dark:text-gray-800"
                          variant="outline"
                          size="sm"
                          onClick={() => tryExample("Design a product detail page for an e-commerce app showing images, description, price, and an \"Add to Cart\" button")}
                        >
                          Try it
                        </Button>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span className="italic">"Create a 3-step onboarding flow: Welcome, Permissions Request, and Profile Setup"</span>
                        <Button
                          className="bg-gray-800 dark:bg-white text-white dark:text-gray-800"
                          variant="outline"
                          size="sm"
                          onClick={() => tryExample("Create a 3-step onboarding flow: Welcome, Permissions Request, and Profile Setup")}
                        >
                          Try it
                        </Button>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span className="italic">"Create a well designed Search Results view of Apartments for rent. Make sure you include a "Check Availability" button for each listing. Design it like it was made by Airbnb. The user's goal is to find an apartment to rent. The business goal is to increase conversions."</span>
                        <Button
                          className="bg-gray-800 dark:bg-white text-white dark:text-gray-800"
                          variant="outline"
                          size="sm"
                          onClick={() => tryExample("Create a well designed Search Results view of Apartments for rent. Make sure you include a \"Check Availability\" button for each listing. Design it like it was made by Airbnb.")}
                        >
                          Try it
                        </Button>
                      </li>
                    </ul>

                    <h4 className="text-sm font-semibold text-white mb-3">More Complex Prompts(20-40secs to generate):</h4>

                    <ul className="list-disc list-inside space-y-2 text-sm">
                      <li className="flex items-center justify-between gap-2">
                        <span className="italic">"I need a really great UX Portfolio site that will impress FANG companies and get me hired. Design 4 common portfolio pages with all the details and content added. Make it look really really good please. Ultra Modern style and dark mode."</span>
                        <Button
                          className="bg-gray-800 dark:bg-white text-white dark:text-gray-800"
                          variant="outline"
                          size="sm"
                          onClick={() => tryExample("I need a really great UX Portfolio site that will impress FANG companies and get me hired. Design 4 common portfolio pages with all the details and content added. Make it look really really good please. Ultra Modern style and dark mode.")}
                        >
                          Try it
                        </Button>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span className="italic">"I'm trying to determine the best way to display a summarization view within an AI conversation chat view. The feature periodically generates a summarization of the conversation. Not sure whats the best interaction and placement for the user to access it. I'll need to see the conversation with and without the summarization view opened. Create 3 variations of approaches."</span>
                        <Button
                          className="bg-gray-800 dark:bg-white text-white dark:text-gray-800"
                          variant="outline"
                          size="sm"
                          onClick={() => tryExample("I'm trying to determine the best way to display a summarization view within an AI conversation chat view. The feature periodically generates a summarization of the conversation. Not sure whats the best interaction and placement for the user to access it. I'll need to see the conversation with and without the summarization view opened. Create 3 variations of approaches.")}
                        >
                          Try it
                        </Button>
                      </li>
                    </ul>

                    <Separator className="my-6 bg-gray-400 h-[1px]" />

                    <h3 className="text-md font-semibold text-white mb-3">Work in Progress:</h3>

                    <ul className="list-disc list-inside space-y-2 text-sm">
                      <li className="flex items-center justify-between gap-2">
                        <span>• Faster output</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• History of Prompts</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Import/Export to Figma</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Refine/Revisions to generated designs with AI</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Edit designs directly on the canvas</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Upload Designs in your prompts</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Desktop Mockups</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Get existing user research/insight from the web</span>
                      </li>
                      <li className="flex items-center justify-between gap-2">
                        <span>• Design Inspiration</span>
                      </li>

                      <li className="flex items-center justify-between gap-2">
                        <span>• Saving and storage</span>
                      </li>
                    </ul>
                  </div>
                </ScrollArea>
              </div>
            </PopoverContent>
          </Popover>
          <History onSelectPrompt={handleSelectPrompt} />
        </div>
        <Button onClick={() => sendMessage()} disabled={isLoading} className="w-10 h-10">
          {isLoading ? <Orbit className="animate-spin" /> : <ArrowUp />}
        </Button>
      </div>


    </div>
  )
} 