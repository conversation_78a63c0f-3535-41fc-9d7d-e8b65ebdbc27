'use client';
import { Tldraw, createShapeId, TLUiComponents, TLEditorComponents, track, useEditor } from 'tldraw'
import { useState, useCallback, useEffect } from 'react';
import { Button } from "./ui/button";
import { Figma, GraduationCap, Rotate3D, Smartphone, Tablet, Monitor } from 'lucide-react'
import ReactMarkdown from 'react-markdown';
// Remove static imports for browser-only libraries
import { documentToSVG, elementToSVG, inlineResources } from 'dom2svg'
import * as htmlToImage from 'html-to-image';
import { toPng, toJpeg, toBlob, toPixelData, toSvg } from 'html-to-image';
import * as domtoimage from "dom-to-image";
import htmlToSvg from "htmlsvg";
import { Iterate } from './Iterate';


interface DesignToolbarProps {
    ResearchData: any;
    htmlData: any;
    id: string;
    editor: any;
    shape: any;
}

export function DesignToolbar({ ResearchData, htmlData, id, editor, shape }: DesignToolbarProps) {
    const [isResearchVisible, setIsResearchVisible] = useState(false);
    const [isIterateVisible, setIsIterateVisible] = useState(false);
    const html = htmlData;
    const researchData = ResearchData;

    const handleSmartphoneClick = useCallback(() => {
        if (editor && shape) {
            editor.updateShape({
                id: shape.id,
                type: shape.type,
                props: {
                    w: 452,
                    h: 874
                },
            });
        }
    }, [editor, shape]);

    const handleTabletClick = useCallback(() => {
        if (editor && shape) {
            editor.updateShape({
                id: shape.id,
                type: shape.type,
                props: {
                    w: 768,
                    h: 1024
                },
            });
        }
    }, [editor, shape]);

    const handleDesktopClick = useCallback(() => {
        if (editor && shape) {
            editor.updateShape({
                id: shape.id,
                type: shape.type,
                props: {
                    w: 1440,
                    h: 900
                },
            });
        }
    }, [editor, shape]);


    const handleCopyToClipboardForFigma = useCallback(async (id: string) => {
        // Only run in browser
        if (typeof window === 'undefined') return;

        console.log('Copying to clipboard...');
        if (!id) {
            console.error('No id provided');
            return;
        }

        const mockUpElement = document.getElementById(`${id}`);
        //const svgDocument = documentToSVG(document)
        //const mockUpElement = window.document.body.querySelector(`#${id}`);
        if (!mockUpElement) {
            console.error('Element not found');
            return;
        }
        try {
            function getComputedStyles(element: HTMLElement) {
                const computedStyle = window.getComputedStyle(element);
                const styles: { [key: string]: string } = {};

                // Iterate over all computed properties
                for (let i = 0; i < computedStyle.length; i++) {
                    const propertyName = computedStyle[i];
                    const propertyValue = computedStyle.getPropertyValue(propertyName);
                    styles[propertyName] = propertyValue;
                }
                return styles;
            }
            const elementStyles = getComputedStyles(mockUpElement);
            console.log('computedStyle: ', elementStyles);
            // Dynamically import browser-only libraries

            const svg = await htmlToSvg(mockUpElement);
            console.log('htmlsvg: ', svg);
            const { elementToSVG, inlineResources } = await import('dom2svg');

            const svgDocument = await elementToSVG(mockUpElement);
            console.log('svgDocument: ', svgDocument);
            //await inlineResources(svgDocument.documentElement);
            const svgString = new XMLSerializer().serializeToString(svgDocument);
            navigator.clipboard.writeText(svgString);
            // htmlToImage
            //     .toSvg(mockUpElement)
            //     .then(function (dataUrl) {
            //         const link = document.createElement('a')
            //         link.download = 'my-image-name.svg'
            //         link.href = dataUrl
            //         link.click()
            //     })

        } catch (error) {
            console.error('Error copying to clipboard:', error);
        }
    }, [id]);

    return (
        <>
            <div id="designToolbar" className="flex flex-col h-20 -mt-25 relative">
                <div className="flex flex-row justify-between h-10 w-full items-center" style={{ pointerEvents: 'all', userSelect: 'none', cursor: 'grab' }} >
                    <h3 className='text-2xl font-bold'>Mockup Title</h3>
                </div>
                <div
                    className='flex flex-row justify-between h-10 w-full  items-center z-1500' style={{ pointerEvents: 'all', }}>
                    {/* Research button on the left */}
                    <Button
                        variant="outline"
                        className="rounded-full"
                        onClick={() => setIsResearchVisible(!isResearchVisible)}
                        onPointerDown={(e) => e.stopPropagation()}
                    >
                        <GraduationCap size={20} />{isResearchVisible ? 'Hide Research' : 'View Research'}
                    </Button>

                    {/* Device size buttons in the center */}
                    <div className="flex flex-row gap-2">
                        <Button variant="outline" className="rounded-full"
                            onClick={handleSmartphoneClick}
                            onPointerDown={(e) => e.stopPropagation()}>
                            <Smartphone size={20} />
                        </Button>
                        <Button variant="outline" className="rounded-full"
                            onClick={handleTabletClick}
                            onPointerDown={(e) => e.stopPropagation()}>
                            <Tablet size={20} />
                        </Button>
                        <Button variant="outline" className="rounded-full"
                            onClick={handleDesktopClick}
                            onPointerDown={(e) => e.stopPropagation()}>
                            <Monitor size={20} />
                        </Button>
                    </div>

                    {/* Figma and Ideate buttons on the right */}
                    <div className="flex flex-row gap-2">
                        <Button variant="outline" className="rounded-full"
                            onClick={() => handleCopyToClipboardForFigma(id)}
                            onPointerDown={(e) => e.stopPropagation()}>
                            <Figma size={20} />
                        </Button>
                        <Button variant="outline" className="rounded-full"
                            onClick={() => setIsIterateVisible(!isIterateVisible)}
                            onPointerDown={(e) => e.stopPropagation()}>
                            <Rotate3D size={20} />Ideate
                        </Button>
                    </div>
                </div>
            </div>
            {isIterateVisible && (
                <Iterate currentHtml={html} shape={shape} />
            )}
            {isResearchVisible && (
                <div id="Research" className="flex flex-col text-normal h-auto w-[500px] bg-[#f6f9fe] absolute z-500 ml-[-500px] mt-4 leading-relaxed p-8 whitespace-break-spaces rounded-tl-2xl border-white border-2">
                    <ReactMarkdown>{researchData}</ReactMarkdown>
                </div>
            )}
        </>

    );
}
